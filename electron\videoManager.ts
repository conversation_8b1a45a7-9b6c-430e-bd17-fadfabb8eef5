import * as fs from 'node:fs'
import * as path from 'node:path'
import { app } from 'electron'
import type { 
  LocalVideoInfo, 
  RemoteVideoInfo, 
  VideoDownloadTask, 
  VideoManagerConfig,
  DownloadProgressEvent,
  DownloadCompleteEvent 
} from '../src/types/interfaces'

/**
 * 视频下载管理器
 */
export class VideoManager {
  private config: VideoManagerConfig
  private downloadTasks: Map<string, VideoDownloadTask> = new Map()
  private activeDownloads: Set<string> = new Set()
  private downloadPath: string

  constructor() {
    this.downloadPath = path.join(app.getPath('userData'), 'videos')
    this.config = {
      baseUrl: 'https://r2.wordcarve.com/live',
      downloadPath: this.downloadPath,
      maxConcurrentDownloads: 3,
      autoRetry: true,
      retryCount: 3,
    }
    
    this.ensureDownloadDirectory()
  }

  /**
   * 确保下载目录存在
   */
  private ensureDownloadDirectory(): void {
    if (!fs.existsSync(this.downloadPath)) {
      fs.mkdirSync(this.downloadPath, { recursive: true })
    }
  }

  /**
   * 获取本地视频列表
   */
  getLocalVideos(): LocalVideoInfo[] {
    try {
      const files = fs.readdirSync(this.downloadPath)
      const videos: LocalVideoInfo[] = []

      for (const file of files) {
        if (this.isVideoFile(file)) {
          const filePath = path.join(this.downloadPath, file)
          const stats = fs.statSync(filePath)
          
          videos.push({
            key: file,
            path: filePath,
            size: stats.size,
            downloadTime: stats.birthtime.toISOString(),
            displayName: this.getDisplayName(file),
          })
        }
      }

      return videos.sort((a, b) => a.displayName.localeCompare(b.displayName))
    }
    catch (error) {
      console.error('Failed to get local videos:', error)
      return []
    }
  }

  /**
   * 开始下载视频
   */
  async startDownload(videoInfo: RemoteVideoInfo): Promise<{ success: boolean; taskId?: string; error?: string }> {
    try {
      // 检查是否已存在
      const localPath = path.join(this.downloadPath, videoInfo.key)
      if (fs.existsSync(localPath)) {
        return { success: false, error: '视频已存在' }
      }

      // 检查并发下载限制
      if (this.activeDownloads.size >= this.config.maxConcurrentDownloads) {
        return { success: false, error: '下载队列已满，请稍后重试' }
      }

      const taskId = this.generateTaskId()
      const task: VideoDownloadTask = {
        id: taskId,
        videoInfo,
        status: 'pending',
        progress: 0,
        downloadedBytes: 0,
        totalBytes: videoInfo.size,
        speed: 0,
        startTime: new Date().toISOString(),
      }

      this.downloadTasks.set(taskId, task)
      this.activeDownloads.add(taskId)

      // 开始下载
      this.performDownload(task)

      return { success: true, taskId }
    }
    catch (error) {
      console.error('Failed to start download:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '下载启动失败' 
      }
    }
  }

  /**
   * 执行下载
   */
  private async performDownload(task: VideoDownloadTask): Promise<void> {
    const localPath = path.join(this.downloadPath, task.videoInfo.key)
    
    try {
      task.status = 'downloading'
      
      const response = await fetch(task.videoInfo.url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const writeStream = fs.createWriteStream(localPath)
      let downloadedBytes = 0
      const startTime = Date.now()

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        writeStream.write(value)
        downloadedBytes += value.length

        // 更新进度
        task.downloadedBytes = downloadedBytes
        task.progress = (downloadedBytes / task.totalBytes) * 100
        task.speed = this.calculateSpeed(downloadedBytes, Date.now() - startTime)

        // 发送进度事件（这里需要通过IPC发送到渲染进程）
        this.emitProgressEvent(task)
      }

      writeStream.end()
      
      // 下载完成
      task.status = 'completed'
      task.endTime = new Date().toISOString()
      task.progress = 100

      this.activeDownloads.delete(task.id)
      this.emitCompleteEvent(task, true, localPath)

    }
    catch (error) {
      console.error('Download failed:', error)
      
      // 清理失败的文件
      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath)
      }

      task.status = 'failed'
      task.error = error instanceof Error ? error.message : '下载失败'
      task.endTime = new Date().toISOString()

      this.activeDownloads.delete(task.id)
      this.emitCompleteEvent(task, false, undefined, task.error)
    }
  }

  /**
   * 暂停下载
   */
  pauseDownload(taskId: string): boolean {
    const task = this.downloadTasks.get(taskId)
    if (task && task.status === 'downloading') {
      task.status = 'paused'
      this.activeDownloads.delete(taskId)
      return true
    }
    return false
  }

  /**
   * 取消下载
   */
  cancelDownload(taskId: string): boolean {
    const task = this.downloadTasks.get(taskId)
    if (task) {
      // 删除未完成的文件
      const localPath = path.join(this.downloadPath, task.videoInfo.key)
      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath)
      }

      this.downloadTasks.delete(taskId)
      this.activeDownloads.delete(taskId)
      return true
    }
    return false
  }

  /**
   * 删除本地视频
   */
  deleteLocalVideo(videoKey: string): boolean {
    try {
      const localPath = path.join(this.downloadPath, videoKey)
      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath)
        return true
      }
      return false
    }
    catch (error) {
      console.error('Failed to delete video:', error)
      return false
    }
  }

  /**
   * 获取下载任务列表
   */
  getDownloadTasks(): VideoDownloadTask[] {
    return Array.from(this.downloadTasks.values())
  }

  /**
   * 计算下载速度
   */
  private calculateSpeed(downloadedBytes: number, elapsedMs: number): number {
    if (elapsedMs === 0) return 0
    return (downloadedBytes / elapsedMs) * 1000 // bytes per second
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查是否为视频文件
   */
  private isVideoFile(filename: string): boolean {
    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
    return videoExtensions.includes(extension)
  }

  /**
   * 获取显示名称
   */
  private getDisplayName(filename: string): string {
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'))
    return nameWithoutExt
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
  }

  /**
   * 发送进度事件（需要在IPC中实现）
   */
  private emitProgressEvent(task: VideoDownloadTask): void {
    // TODO: 实现进度事件发送到渲染进程
    console.log(`Download progress: ${task.progress}% for ${task.videoInfo.key}`)
  }

  /**
   * 发送完成事件（需要在IPC中实现）
   */
  private emitCompleteEvent(task: VideoDownloadTask, success: boolean, localPath?: string, error?: string): void {
    // TODO: 实现完成事件发送到渲染进程
    console.log(`Download ${success ? 'completed' : 'failed'} for ${task.videoInfo.key}`)
  }
}

// 创建全局实例
export const videoManager = new VideoManager()
