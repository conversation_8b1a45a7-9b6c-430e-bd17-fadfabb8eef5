<script setup lang="ts">
import type { LocalVideoInfo, RemoteVideoInfo } from '../../types/interfaces'
import { ElButton, ElTable, ElTableColumn, ElTag } from 'element-plus'
import { VideoService } from '../../utils/videoService'

// Props
interface Props {
  videos: RemoteVideoInfo[]
  localVideos: LocalVideoInfo[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// Emits
interface Emits {
  (e: 'download-video', video: RemoteVideoInfo): void
  (e: 'refresh-list'): void
}

// 下载视频
function downloadVideo(video: RemoteVideoInfo) {
  emit('download-video', video)
}

// 刷新列表
function refreshList() {
  emit('refresh-list')
}

// 检查视频是否已下载
function isVideoDownloaded(videoKey: string): boolean {
  return props.localVideos.some(v => v.key === videoKey)
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  return VideoService.formatFileSize(bytes)
}

// 格式化日期
function formatDate(dateString: string): string {
  return VideoService.formatDate(dateString)
}
</script>

<template>
  <div class="remote-video-list">
    <div class="mb-4 flex items-center justify-between">
      <h4 class="text-lg font-medium">
        在线视频库 ({{ videos.length }})
      </h4>
      <ElButton
        :loading="loading"
        @click="refreshList"
      >
        刷新列表
      </ElButton>
    </div>

    <div v-if="loading" class="py-8 text-center">
      <div class="loading-spinner mx-auto mb-4" />
      <div class="text-gray-500">
        加载视频列表中...
      </div>
    </div>

    <div v-else-if="videos.length === 0" class="py-8 text-center text-gray-500">
      暂无可用视频，请点击刷新列表重试
    </div>

    <ElTable v-else :data="videos" stripe class="w-full">
      <ElTableColumn prop="displayName" label="视频名称" min-width="200">
        <template #default="{ row }">
          <div class="flex items-center">
            <span class="font-medium">{{ row.displayName }}</span>
            <ElTag v-if="isVideoDownloaded(row.key)" type="success" size="small" class="ml-2">
              已下载
            </ElTag>
          </div>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="size" label="文件大小" width="120">
        <template #default="{ row }">
          <span class="text-sm">{{ formatFileSize(row.size) }}</span>
        </template>
      </ElTableColumn>

      <ElTableColumn prop="lastModified" label="更新时间" width="160">
        <template #default="{ row }">
          <span class="text-sm text-gray-600">{{ formatDate(row.lastModified) }}</span>
        </template>
      </ElTableColumn>

      <ElTableColumn label="状态" width="100">
        <template #default="{ row }">
          <ElTag v-if="isVideoDownloaded(row.key)" type="success">
            已下载
          </ElTag>
          <ElTag v-else type="info">
            未下载
          </ElTag>
        </template>
      </ElTableColumn>

      <ElTableColumn label="操作" width="120">
        <template #default="{ row }">
          <ElButton
            v-if="!isVideoDownloaded(row.key)"
            type="primary"
            size="small"
            @click="downloadVideo(row)"
          >
            下载
          </ElButton>
          <ElButton
            v-else
            type="success"
            size="small"
            disabled
          >
            已下载
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<style scoped>
.remote-video-list {
  margin-top: 24px;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--el-border-color-lighter);
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table__row:hover) {
  background-color: var(--el-color-primary-light-9);
}
</style>
