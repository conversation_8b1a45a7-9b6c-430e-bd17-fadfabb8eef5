<script setup lang="ts">
import type { BackgroundConfig, LocalVideoInfo, RemoteVideoInfo } from '../types/interfaces'
import { ElButton, ElCard, ElColorPicker, ElDivider, ElForm, ElFormItem, ElMessage, ElOption, ElRadio, ElRadioGroup, ElSelect, ElSlider, ElTable, ElTableColumn, ElTag } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { deleteLocalVideo, getConfig, getLocalVideos, saveConfig, startVideoDownload } from '../utils/ipc'
import { VideoService, videoService } from '../utils/videoService'

const backgroundConfig = ref<BackgroundConfig>({
  type: 'css',
  cssEffect: 'aurora',
  opacity: 0.8,
  speed: 1,
  colors: ['#7877c6', '#4f46e5', '#06b6d4'],
})

const isLoading = ref(false)
let debounceTimer: number | undefined

// 视频管理相关状态
const remoteVideos = ref<RemoteVideoInfo[]>([])
const localVideos = ref<LocalVideoInfo[]>([])
const isLoadingVideos = ref(false)
const selectedVideoKey = ref<string>('')

// 背景类型选项
const backgroundTypeOptions = [
  { label: 'CSS动效', value: 'css' },
  { label: '视频背景', value: 'video' },
]

// CSS 效果选项
const cssEffectOptions = [
  { label: '极光效果', value: 'aurora' },
  { label: '渐变效果', value: 'gradient' },
  { label: '粒子效果', value: 'particles' },
  { label: '矩阵效果', value: 'matrix' },
]

// 预设配置
const presetConfigs = [
  {
    name: '经典极光',
    config: { type: 'css', cssEffect: 'aurora', opacity: 0.8, speed: 1, colors: ['#7877c6', '#4f46e5', '#06b6d4'] },
  },
  {
    name: '科技矩阵',
    config: { type: 'css', cssEffect: 'matrix', opacity: 0.6, speed: 1.5, colors: ['#00ff00'] },
  },
  {
    name: '粒子星空',
    config: { type: 'css', cssEffect: 'particles', opacity: 0.7, speed: 0.8, colors: ['#7877c6', '#4f46e5'] },
  },
  {
    name: '彩虹渐变',
    config: { type: 'css', cssEffect: 'gradient', opacity: 0.9, speed: 2, colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'] },
  },
]

// 加载配置
async function loadConfig() {
  isLoading.value = true
  try {
    const result = await getConfig({ silent: true })
    if (result.success && result.data?.shutdownBackground) {
      backgroundConfig.value = { ...backgroundConfig.value, ...result.data.shutdownBackground }
    }
  }
  catch (error) {
    console.error('Failed to load background config:', error)
    ElMessage.error('加载配置失败')
  }
  finally {
    isLoading.value = false
  }
}

// 保存配置
async function saveBackgroundConfig(silent = false) {
  isLoading.value = true
  try {
    const result = await getConfig({ silent: true })
    const currentConfig = result.success ? result.data : {}
    const newConfig = { ...currentConfig, shutdownBackground: backgroundConfig.value }
    const saveResult = await saveConfig(newConfig, { silent: true })

    if (saveResult.success) {
      if (!silent)
        ElMessage.success('背景配置保存成功')
    }
    else {
      if (!silent)
        ElMessage.error('保存配置失败')
    }
  }
  catch (error) {
    console.error('Failed to save background config:', error)
    if (!silent)
      ElMessage.error('保存配置失败')
  }
  finally {
    isLoading.value = false
  }
}

// 应用预设
function applyPreset(preset: any) {
  backgroundConfig.value = { ...backgroundConfig.value, ...preset.config }
  ElMessage.success(`已应用预设：${preset.name}`)
}

// 重置为默认配置
function resetToDefault() {
  backgroundConfig.value = {
    type: 'css',
    cssEffect: 'aurora',
    opacity: 0.8,
    speed: 1,
    colors: ['#7877c6', '#4f46e5', '#06b6d4'],
  }
  ElMessage.success('已重置为默认配置')
}

// 添加颜色
function addColor() {
  if (!backgroundConfig.value.colors)
    backgroundConfig.value.colors = []
  backgroundConfig.value.colors.push('#ffffff')
}

// 移除颜色
function removeColor(index: number) {
  if (backgroundConfig.value.colors && backgroundConfig.value.colors.length > 1) {
    backgroundConfig.value.colors.splice(index, 1)
  }
}

// ==================== 视频管理相关方法 ====================

// 加载远程视频列表
async function loadRemoteVideos() {
  isLoadingVideos.value = true
  try {
    const result = await videoService.getVideoList()
    if (result.success) {
      remoteVideos.value = result.videos
    }
    else {
      ElMessage.error(result.error || '获取视频列表失败')
    }
  }
  catch (error) {
    console.error('Failed to load remote videos:', error)
    ElMessage.error('获取视频列表失败')
  }
  finally {
    isLoadingVideos.value = false
  }
}

// 加载本地视频列表
async function loadLocalVideos() {
  try {
    const result = await getLocalVideos({ silent: true })
    if (result.success && result.data) {
      localVideos.value = result.data
    }
  }
  catch (error) {
    console.error('Failed to load local videos:', error)
  }
}

// 下载视频
async function downloadVideo(video: RemoteVideoInfo) {
  try {
    const result = await startVideoDownload(video, { silent: false })
    if (result.success) {
      // 下载开始后刷新本地视频列表
      setTimeout(() => {
        loadLocalVideos()
      }, 1000)
    }
  }
  catch (error) {
    console.error('Failed to download video:', error)
    ElMessage.error('下载视频失败')
  }
}

// 删除本地视频
async function deleteVideo(videoKey: string) {
  try {
    const result = await deleteLocalVideo(videoKey, { silent: false })
    if (result.success) {
      await loadLocalVideos()
      // 如果删除的是当前选中的视频，清空选择
      if (backgroundConfig.value.videoPath?.includes(videoKey)) {
        backgroundConfig.value.videoPath = ''
        backgroundConfig.value.type = 'css'
      }
    }
  }
  catch (error) {
    console.error('Failed to delete video:', error)
    ElMessage.error('删除视频失败')
  }
}

// 选择视频作为背景
function selectVideo(video: LocalVideoInfo) {
  backgroundConfig.value.type = 'video'
  backgroundConfig.value.videoPath = video.path
  selectedVideoKey.value = video.key
  ElMessage.success(`已选择视频：${video.displayName}`)
}

// 检查视频是否已下载
function isVideoDownloaded(videoKey: string): boolean {
  return localVideos.value.some(v => v.key === videoKey)
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  return VideoService.formatFileSize(bytes)
}

// 格式化日期
function formatDate(dateString: string): string {
  return VideoService.formatDate(dateString)
}

watch(backgroundConfig, () => {
  clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    saveBackgroundConfig(true)
  }, 1000)
}, { deep: true })

onMounted(() => {
  loadConfig()
  loadLocalVideos()
  loadRemoteVideos()
})
</script>

<template>
  <div class="background-settings-modern">
    <ElCard class="settings-card" shadow="never">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold">
            关机页背景设置
          </h3>
          <div class="flex gap-2">
            <ElButton type="primary" @click="() => saveBackgroundConfig(false)">
              保存配置
            </ElButton>
            <ElButton @click="resetToDefault">
              重置为默认
            </ElButton>
          </div>
        </div>
      </template>

      <ElForm :model="backgroundConfig" label-position="top" class="space-y-6">
        <!-- 背景类型选择 -->
        <ElFormItem label="背景类型" class="mb-0">
          <ElRadioGroup v-model="backgroundConfig.type" class="w-full">
            <ElRadio v-for="option in backgroundTypeOptions" :key="option.value" :value="option.value" class="mr-4">
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- CSS效果配置 -->
        <div v-if="backgroundConfig.type === 'css'" class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <ElFormItem label="效果类型" class="mb-0">
            <ElSelect v-model="backgroundConfig.cssEffect" placeholder="选择CSS效果" class="w-full">
              <ElOption v-for="option in cssEffectOptions" :key="option.value" :label="option.label" :value="option.value" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="颜色配置" class="mb-0">
            <div class="w-full space-y-4">
              <div v-for="(_, index) in backgroundConfig.colors" :key="index" class="flex items-center gap-4">
                <ElColorPicker v-model="backgroundConfig.colors![index]" size="large" />
                <div class="min-w-0 flex-1">
                  <span class="text-sm text-gray-600 font-medium">颜色 {{ index + 1 }}</span>
                </div>
                <ElButton
                  v-if="backgroundConfig.colors!.length > 1"
                  type="danger"
                  text
                  size="small"
                  @click="removeColor(index)"
                >
                  移除
                </ElButton>
              </div>
              <ElButton
                type="primary"
                text
                class="w-full md:w-auto"
                size="small"
                @click="addColor"
              >
                <span class="i-carbon-add mr-1 inline-block" />
                添加颜色
              </ElButton>
            </div>
          </ElFormItem>
        </div>

        <!-- 视频背景配置 -->
        <div v-if="backgroundConfig.type === 'video'" class="space-y-6">
          <ElDivider content-position="left">
            视频管理
          </ElDivider>

          <!-- 本地视频列表 -->
          <div v-if="localVideos.length > 0">
            <h4 class="mb-4 text-lg font-medium">
              本地视频 ({{ localVideos.length }})
            </h4>
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
              <div
                v-for="video in localVideos"
                :key="video.key"
                class="video-card"
                :class="{ selected: selectedVideoKey === video.key }"
                @click="selectVideo(video)"
              >
                <div class="video-info">
                  <div class="video-name">
                    {{ video.displayName }}
                  </div>
                  <div class="video-meta">
                    <span>{{ formatFileSize(video.size) }}</span>
                    <span>{{ formatDate(video.downloadTime) }}</span>
                  </div>
                </div>
                <div class="video-actions">
                  <ElButton
                    type="danger"
                    size="small"
                    text
                    @click.stop="deleteVideo(video.key)"
                  >
                    删除
                  </ElButton>
                </div>
              </div>
            </div>
          </div>

          <!-- 远程视频列表 -->
          <div>
            <div class="mb-4 flex items-center justify-between">
              <h4 class="text-lg font-medium">
                在线视频库 ({{ remoteVideos.length }})
              </h4>
              <ElButton
                :loading="isLoadingVideos"
                @click="loadRemoteVideos"
              >
                刷新列表
              </ElButton>
            </div>

            <div v-if="isLoadingVideos" class="py-8 text-center">
              <div class="loading-spinner mx-auto mb-4" />
              <div class="text-gray-500">
                加载视频列表中...
              </div>
            </div>

            <ElTable v-else :data="remoteVideos" stripe class="w-full">
              <ElTableColumn prop="displayName" label="视频名称" min-width="200" />
              <ElTableColumn prop="size" label="文件大小" width="120">
                <template #default="{ row }">
                  {{ formatFileSize(row.size) }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="lastModified" label="更新时间" width="160">
                <template #default="{ row }">
                  {{ formatDate(row.lastModified) }}
                </template>
              </ElTableColumn>
              <ElTableColumn label="状态" width="100">
                <template #default="{ row }">
                  <ElTag v-if="isVideoDownloaded(row.key)" type="success">
                    已下载
                  </ElTag>
                  <ElTag v-else type="info">
                    未下载
                  </ElTag>
                </template>
              </ElTableColumn>
              <ElTableColumn label="操作" width="120">
                <template #default="{ row }">
                  <ElButton
                    v-if="!isVideoDownloaded(row.key)"
                    type="primary"
                    size="small"
                    @click="downloadVideo(row)"
                  >
                    下载
                  </ElButton>
                  <ElButton
                    v-else
                    type="success"
                    size="small"
                    disabled
                  >
                    已下载
                  </ElButton>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </div>

        <ElDivider v-if="backgroundConfig.type === 'css'" content-position="left" class="mt-8">
          一键应用预设
        </ElDivider>
        <div v-if="backgroundConfig.type === 'css'" class="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div v-for="preset in presetConfigs" :key="preset.name" class="preset-card" @click="applyPreset(preset)">
            <div
              class="preset-preview"
              :style="{ background: `linear-gradient(45deg, ${preset.config.colors.join(', ')})` }"
            />
            <div class="mt-2 text-center">
              <div class="font-medium">
                {{ preset.name }}
              </div>
              <div class="text-xs text-gray-500">
                点击应用
              </div>
            </div>
          </div>
        </div>

        <ElDivider content-position="left" class="mt-8">
          通用设置
        </ElDivider>
        <div class="grid grid-cols-1 gap-x-8 gap-y-4 md:grid-cols-2">
          <ElFormItem label="背景不透明度">
            <ElSlider v-model="backgroundConfig.opacity" :min="0.1" :max="1" :step="0.05" :format-tooltip="(val: number) => `${Math.round(val * 100)}%`" />
          </ElFormItem>
          <ElFormItem label="动画速度">
            <ElSlider v-model="backgroundConfig.speed" :min="0.1" :max="3" :step="0.1" :format-tooltip="(val: number) => `${val}x`" />
          </ElFormItem>
        </div>
      </ElForm>
    </ElCard>
  </div>
</template>

<style scoped>
.background-settings-modern {
  max-width: 800px;
  margin: auto;
}

.settings-card {
  border: none;
}

.preset-card {
  cursor: pointer;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preset-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.preset-preview {
  width: 100%;
  height: 60px;
  border-radius: 6px;
  background-color: #f0f2f5;
}

.el-form--label-top .el-form-item {
  margin-bottom: 0;
}

.mt-8 {
  margin-top: 2rem;
}

.el-color-picker {
  border: none;
  --el-color-picker-width: 40px;
  --el-color-picker-height: 40px;
}

/* 视频卡片样式 */
.video-card {
  cursor: pointer;
  border: 2px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease-in-out;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
}

.video-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.video-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.video-info {
  flex: 1;
}

.video-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.video-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--el-border-color-lighter);
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
