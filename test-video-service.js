/**
 * 视频服务测试脚本
 * 用于验证视频管理器的基本功能
 */

// 模拟测试环境
const testVideoService = {
  baseUrl: 'https://r2.wordcarve.com/live',
  
  // 测试XML解析功能
  async testXmlParsing() {
    console.log('🧪 测试XML解析功能...')
    
    // 模拟S3 XML响应
    const mockXmlResponse = `<?xml version="1.0" encoding="UTF-8"?>
<ListBucketResult xmlns="http://s3.amazonaws.com/doc/2006-03-01/">
  <Name>live</Name>
  <Contents>
    <Key>sample-video-1.mp4</Key>
    <Size>15728640</Size>
    <LastModified>2024-01-15T10:30:00.000Z</LastModified>
  </Contents>
  <Contents>
    <Key>sample-video-2.avi</Key>
    <Size>25165824</Size>
    <LastModified>2024-01-16T14:20:00.000Z</LastModified>
  </Contents>
  <Contents>
    <Key>readme.txt</Key>
    <Size>1024</Size>
    <LastModified>2024-01-10T09:15:00.000Z</LastModified>
  </Contents>
</ListBucketResult>`

    try {
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(mockXmlResponse, 'text/xml')
      const contents = xmlDoc.getElementsByTagName('Contents')
      
      const videos = []
      for (let i = 0; i < contents.length; i++) {
        const content = contents[i]
        const key = content.getElementsByTagName('Key')[0]?.textContent || ''
        const size = parseInt(content.getElementsByTagName('Size')[0]?.textContent || '0')
        const lastModified = content.getElementsByTagName('LastModified')[0]?.textContent || ''
        
        // 过滤视频文件
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
        if (videoExtensions.some(ext => key.toLowerCase().endsWith(ext))) {
          videos.push({
            key,
            size,
            lastModified,
            url: `${this.baseUrl}/${key}`,
            displayName: key.replace(/\.[^/.]+$/, '') // 去掉扩展名
          })
        }
      }
      
      console.log('✅ XML解析成功')
      console.log('📹 找到视频文件:', videos.length)
      videos.forEach(video => {
        console.log(`  - ${video.displayName} (${this.formatFileSize(video.size)})`)
      })
      
      return { success: true, videos }
    } catch (error) {
      console.error('❌ XML解析失败:', error)
      return { success: false, error: error.message }
    }
  },
  
  // 测试文件大小格式化
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
  
  // 测试日期格式化
  formatDate(dateString) {
    try {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return dateString
    }
  },
  
  // 测试网络连接
  async testNetworkConnection() {
    console.log('🌐 测试网络连接...')
    
    try {
      const response = await fetch(this.baseUrl, {
        method: 'HEAD',
        mode: 'no-cors' // 避免CORS问题
      })
      console.log('✅ 网络连接正常')
      return true
    } catch (error) {
      console.log('⚠️ 网络连接测试失败 (可能是CORS限制):', error.message)
      return false
    }
  },
  
  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始视频服务测试...\n')
    
    // 测试1: XML解析
    const xmlTest = await this.testXmlParsing()
    console.log('')
    
    // 测试2: 网络连接
    const networkTest = await this.testNetworkConnection()
    console.log('')
    
    // 测试3: 工具函数
    console.log('🔧 测试工具函数...')
    console.log('文件大小格式化:')
    console.log(`  - 1024 bytes = ${this.formatFileSize(1024)}`)
    console.log(`  - 1048576 bytes = ${this.formatFileSize(1048576)}`)
    console.log(`  - 15728640 bytes = ${this.formatFileSize(15728640)}`)
    
    console.log('日期格式化:')
    console.log(`  - 2024-01-15T10:30:00.000Z = ${this.formatDate('2024-01-15T10:30:00.000Z')}`)
    console.log('✅ 工具函数测试完成')
    console.log('')
    
    // 总结
    console.log('📊 测试总结:')
    console.log(`  - XML解析: ${xmlTest.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`  - 网络连接: ${networkTest ? '✅ 通过' : '⚠️ 警告'}`)
    console.log(`  - 工具函数: ✅ 通过`)
    
    return {
      xmlParsing: xmlTest.success,
      networkConnection: networkTest,
      utilityFunctions: true
    }
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testVideoService = testVideoService
  console.log('💡 在浏览器控制台中运行: testVideoService.runAllTests()')
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testVideoService
}

// 自动运行测试（如果直接执行此文件）
if (typeof window !== 'undefined' && window.location) {
  // 浏览器环境，等待用户手动调用
} else if (typeof require !== 'undefined' && require.main === module) {
  // Node.js环境，自动运行
  testVideoService.runAllTests()
}
