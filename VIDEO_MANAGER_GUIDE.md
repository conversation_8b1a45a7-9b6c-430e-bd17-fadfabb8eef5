# 视频管理器使用指南

## 功能概述

新的视频管理器为哪吒桌面助手添加了动态视频背景功能，用户可以：

1. 从在线视频库浏览和下载视频
2. 管理本地已下载的视频
3. 选择视频作为关机页面的动态背景
4. 实时预览视频效果

## 功能特性

### 🎥 在线视频库
- 自动从 `https://r2.wordcarve.com/live` 获取可用视频列表
- 显示视频名称、文件大小、更新时间
- 支持一键下载到本地
- 智能去重，避免重复下载

### 📁 本地视频管理
- 查看已下载的视频列表
- 显示视频信息（大小、下载时间）
- 支持删除不需要的视频
- 一键选择作为背景

### 🎨 视频背景播放
- 自动循环播放
- 支持调节透明度和播放速度
- 智能适配屏幕尺寸
- 错误处理和加载状态显示

## 使用方法

### 1. 访问视频管理界面

1. 启动哪吒桌面助手
2. 在主界面点击"背景设置"标签页
3. 选择"视频背景"类型

### 2. 下载视频

1. 在"在线视频库"部分浏览可用视频
2. 点击"刷新列表"获取最新视频
3. 对于未下载的视频，点击"下载"按钮
4. 等待下载完成（会显示在本地视频列表中）

### 3. 设置视频背景

1. 在"本地视频"部分选择已下载的视频
2. 点击视频卡片选择作为背景
3. 调整透明度和播放速度（可选）
4. 保存配置

### 4. 预览效果

- 配置保存后，在关机确认页面可以看到视频背景效果
- 视频会自动循环播放
- 支持静音播放，不会干扰用户

## 技术实现

### 架构设计
```
前端 (Vue 3) ←→ IPC 通信 ←→ Electron 主进程
     ↓                           ↓
视频管理界面                  文件下载管理
视频背景组件                  本地存储管理
```

### 核心组件

1. **VideoService** - 处理S3 API调用和XML解析
2. **VideoManager** - 管理下载任务和本地文件
3. **VideoBackground** - 视频背景播放组件
4. **BackgroundSettings** - 视频管理界面

### 文件存储

- 视频文件存储在：`{userData}/videos/`
- 支持的格式：`.mp4`, `.avi`, `.mov`, `.wmv`, `.flv`, `.webm`, `.mkv`
- 自动创建目录结构

## 配置说明

### 背景配置结构
```typescript
interface BackgroundConfig {
  type: 'video'           // 背景类型
  videoPath?: string      // 本地视频文件路径
  opacity?: number        // 透明度 (0.1-1.0)
  speed?: number          // 播放速度 (0.1-3.0)
}
```

### 视频信息结构
```typescript
interface RemoteVideoInfo {
  key: string            // 文件名
  size: number           // 文件大小
  lastModified: string   // 最后修改时间
  url: string            // 下载URL
  displayName: string    // 显示名称
}
```

## 故障排除

### 常见问题

1. **无法获取视频列表**
   - 检查网络连接
   - 确认 S3 服务可访问
   - 查看控制台错误信息

2. **下载失败**
   - 检查磁盘空间
   - 确认网络稳定性
   - 重试下载

3. **视频无法播放**
   - 确认文件格式支持
   - 检查文件是否损坏
   - 查看浏览器控制台错误

4. **背景不显示**
   - 确认已选择视频背景类型
   - 检查视频文件路径是否正确
   - 重新选择视频文件

### 调试信息

开发模式下，可以在控制台查看详细的调试信息：
- 视频下载进度
- 文件操作日志
- 错误详情

## 未来计划

- [ ] 支持视频预览缩略图
- [ ] 添加下载进度显示
- [ ] 支持断点续传
- [ ] 添加视频分类和标签
- [ ] 支持自定义视频源
- [ ] 添加视频播放控制（暂停/继续）

## 技术支持

如遇到问题，请：
1. 查看控制台错误信息
2. 检查网络连接状态
3. 确认文件权限设置
4. 联系开发团队获取支持
