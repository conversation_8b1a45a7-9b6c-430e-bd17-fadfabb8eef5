<script setup lang="ts">
import type { LocalVideoInfo } from '../../types/interfaces'
import { ElButton, ElMessage } from 'element-plus'
import { VideoService } from '../../utils/videoService'

// Props
interface Props {
  videos: LocalVideoInfo[]
  selectedVideoKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedVideoKey: '',
})

// Emits
interface Emits {
  (e: 'video-selected', video: LocalVideoInfo): void
  (e: 'video-deleted', videoKey: string): void
}

const emit = defineEmits<Emits>()

// 选择视频
function selectVideo(video: LocalVideoInfo) {
  emit('video-selected', video)
  ElMessage.success(`已选择视频：${video.displayName}`)
}

// 删除视频
function deleteVideo(videoKey: string) {
  emit('video-deleted', videoKey)
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  return VideoService.formatFileSize(bytes)
}

// 格式化日期
function formatDate(dateString: string): string {
  return VideoService.formatDate(dateString)
}
</script>

<template>
  <div class="local-video-list">
    <div v-if="videos.length > 0">
      <h4 class="mb-4 text-lg font-medium">
        本地视频 ({{ videos.length }})
      </h4>
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
        <div
          v-for="video in videos"
          :key="video.key"
          class="video-card"
          :class="{ selected: selectedVideoKey === video.key }"
          @click="selectVideo(video)"
        >
          <div class="video-info">
            <div class="video-name">
              {{ video.displayName }}
            </div>
            <div class="video-meta">
              <span>{{ formatFileSize(video.size) }}</span>
              <span>{{ formatDate(video.downloadTime) }}</span>
            </div>
          </div>
          <div class="video-actions">
            <ElButton
              type="danger"
              size="small"
              text
              @click.stop="deleteVideo(video.key)"
            >
              删除
            </ElButton>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="py-8 text-center text-gray-500">
      暂无本地视频，请先从在线视频库下载
    </div>
  </div>
</template>

<style scoped>
.local-video-list {
  margin-bottom: 24px;
}

/* 视频卡片样式 */
.video-card {
  cursor: pointer;
  border: 2px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease-in-out;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
}

.video-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.video-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.video-info {
  flex: 1;
}

.video-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.video-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}
</style>
