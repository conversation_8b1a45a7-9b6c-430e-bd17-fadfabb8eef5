<script setup lang="ts">
import type { LocalVideoInfo, RemoteVideoInfo } from '../../types/interfaces'
import { ElButton, ElMessage, ElTable, ElTableColumn, ElTag } from 'element-plus'
import { onMounted, ref } from 'vue'
import { deleteLocalVideo, getLocalVideos, startVideoDownload } from '../../utils/ipc'
import { VideoService, videoService } from '../../utils/videoService'

// Props
interface Props {
  selectedVideoKey?: string
}

// Emits
interface Emits {
  (e: 'videoSelected', video: LocalVideoInfo): void
  (e: 'videoDeleted', videoKey: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const remoteVideos = ref<RemoteVideoInfo[]>([])
const localVideos = ref<LocalVideoInfo[]>([])
const isLoadingVideos = ref(false)

// 加载远程视频列表
async function loadRemoteVideos() {
  isLoadingVideos.value = true
  try {
    console.log('开始加载远程视频列表...')
    const result = await videoService.getVideoList()
    console.log('远程视频列表结果:', result)

    if (result.success) {
      remoteVideos.value = result.videos
      console.log(`成功加载 ${result.videos.length} 个远程视频`)
    }
    else {
      console.error('获取视频列表失败:', result.error)
      ElMessage.error(result.error || '获取视频列表失败')
    }
  }
  catch (error) {
    console.error('Failed to load remote videos:', error)
    ElMessage.error('获取视频列表失败')
  }
  finally {
    isLoadingVideos.value = false
  }
}

// 加载本地视频列表
async function loadLocalVideos() {
  try {
    console.log('开始加载本地视频列表...')
    const result = await getLocalVideos({ silent: true })
    console.log('本地视频列表结果:', result)

    if (result.success && result.data) {
      localVideos.value = result.data
      console.log(`成功加载 ${result.data.length} 个本地视频`)
    }
  }
  catch (error) {
    console.error('Failed to load local videos:', error)
  }
}

// 下载视频
async function downloadVideo(video: RemoteVideoInfo) {
  try {
    console.log('开始下载视频:', video.key)
    const result = await startVideoDownload(video, { silent: false })
    if (result.success) {
      ElMessage.success(`开始下载: ${video.displayName}`)
      // 下载开始后刷新本地视频列表
      setTimeout(() => {
        loadLocalVideos()
      }, 1000)
    }
  }
  catch (error) {
    console.error('Failed to download video:', error)
    ElMessage.error('下载视频失败')
  }
}

// 删除本地视频
async function deleteVideo(videoKey: string) {
  try {
    console.log('删除视频:', videoKey)
    const result = await deleteLocalVideo(videoKey, { silent: false })
    if (result.success) {
      await loadLocalVideos()
      emit('videoDeleted', videoKey)
    }
  }
  catch (error) {
    console.error('Failed to delete video:', error)
    ElMessage.error('删除视频失败')
  }
}

// 选择视频
function selectVideo(video: LocalVideoInfo) {
  console.log('选择视频:', video.key)
  emit('videoSelected', video)
}

// 检查视频是否已下载
function isVideoDownloaded(videoKey: string): boolean {
  return localVideos.value.some(v => v.key === videoKey)
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  return VideoService.formatFileSize(bytes)
}

// 格式化日期
function formatDate(dateString: string): string {
  return VideoService.formatDate(dateString)
}

// 初始化
onMounted(() => {
  console.log('VideoManager 组件已挂载')
  loadLocalVideos()
  loadRemoteVideos()
})

// 暴露方法给父组件
defineExpose({
  loadRemoteVideos,
  loadLocalVideos,
  remoteVideos,
  localVideos,
})
</script>

<template>
  <div class="video-manager">
    <!-- 本地视频列表 -->
    <div class="local-videos mb-6">
      <div v-if="localVideos.length > 0">
        <h4 class="mb-4 text-lg font-medium">
          本地视频 ({{ localVideos.length }})
        </h4>
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
          <div
            v-for="video in localVideos"
            :key="video.key"
            class="video-card"
            :class="{ selected: props.selectedVideoKey === video.key }"
            @click="selectVideo(video)"
          >
            <div class="video-info">
              <div class="video-name">
                {{ video.displayName }}
              </div>
              <div class="video-meta">
                <span>{{ formatFileSize(video.size) }}</span>
                <span>{{ formatDate(video.downloadTime) }}</span>
              </div>
            </div>
            <div class="video-actions">
              <ElButton
                type="danger"
                size="small"
                text
                @click.stop="deleteVideo(video.key)"
              >
                删除
              </ElButton>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="py-8 text-center text-gray-500">
        暂无本地视频，请先从在线视频库下载
      </div>
    </div>

    <!-- 远程视频列表 -->
    <div class="remote-videos">
      <div class="mb-4 flex items-center justify-between">
        <h4 class="text-lg font-medium">
          在线视频库 ({{ remoteVideos.length }})
        </h4>
        <ElButton
          :loading="isLoadingVideos"
          @click="loadRemoteVideos"
        >
          刷新列表
        </ElButton>
      </div>

      <div v-if="isLoadingVideos" class="py-8 text-center">
        <div class="loading-spinner mx-auto mb-4" />
        <div class="text-gray-500">
          加载视频列表中...
        </div>
      </div>

      <div v-else-if="remoteVideos.length === 0" class="py-8 text-center text-gray-500">
        暂无可用视频
      </div>

      <ElTable v-else :data="remoteVideos" stripe class="w-full">
        <ElTableColumn prop="displayName" label="视频名称" min-width="200" />
        <ElTableColumn prop="size" label="文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.size) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="lastModified" label="更新时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.lastModified) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="状态" width="100">
          <template #default="{ row }">
            <ElTag v-if="isVideoDownloaded(row.key)" type="success">
              已下载
            </ElTag>
            <ElTag v-else type="info">
              未下载
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="120">
          <template #default="{ row }">
            <ElButton
              v-if="!isVideoDownloaded(row.key)"
              type="primary"
              size="small"
              @click="downloadVideo(row)"
            >
              下载
            </ElButton>
            <ElButton
              v-else
              type="success"
              size="small"
              disabled
            >
              已下载
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>
  </div>
</template>

<style scoped>
.video-manager {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.remote-videos {
  margin-top: 24px;
}

/* 视频卡片样式 */
.video-card {
  cursor: pointer;
  border: 2px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease-in-out;
  background: var(--el-bg-color);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
}

.video-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.video-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.video-info {
  flex: 1;
}

.video-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.video-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--el-border-color-lighter);
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
