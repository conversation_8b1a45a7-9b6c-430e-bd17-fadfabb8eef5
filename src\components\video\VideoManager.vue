<script setup lang="ts">
import type { LocalVideoInfo, RemoteVideoInfo } from '../../types/interfaces'
import { ElButton, ElDivider, ElMessage, ElTable, ElTableColumn, ElTag } from 'element-plus'
import { onMounted, ref } from 'vue'
import { deleteLocalVideo, getLocalVideos, startVideoDownload } from '../../utils/ipc'
import { VideoService, videoService } from '../../utils/videoService'
import LocalVideoList from './LocalVideoList.vue'

// Props
interface Props {
  selectedVideoKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedVideoKey: '',
})

// Emits
interface Emits {
  (e: 'video-selected', video: LocalVideoInfo): void
  (e: 'video-deleted', videoKey: string): void
}

const emit = defineEmits<Emits>()

// 状态管理
const remoteVideos = ref<RemoteVideoInfo[]>([])
const localVideos = ref<LocalVideoInfo[]>([])
const isLoadingVideos = ref(false)

// 加载远程视频列表
async function loadRemoteVideos() {
  isLoadingVideos.value = true
  try {
    console.log('开始加载远程视频列表...')
    const result = await videoService.getVideoList()
    console.log('远程视频列表结果:', result)
    
    if (result.success) {
      remoteVideos.value = result.videos
      console.log(`成功加载 ${result.videos.length} 个远程视频`)
    } else {
      console.error('获取视频列表失败:', result.error)
      ElMessage.error(result.error || '获取视频列表失败')
    }
  } catch (error) {
    console.error('Failed to load remote videos:', error)
    ElMessage.error('获取视频列表失败')
  } finally {
    isLoadingVideos.value = false
  }
}

// 加载本地视频列表
async function loadLocalVideos() {
  try {
    console.log('开始加载本地视频列表...')
    const result = await getLocalVideos({ silent: true })
    console.log('本地视频列表结果:', result)
    
    if (result.success && result.data) {
      localVideos.value = result.data
      console.log(`成功加载 ${result.data.length} 个本地视频`)
    }
  } catch (error) {
    console.error('Failed to load local videos:', error)
  }
}

// 下载视频
async function downloadVideo(video: RemoteVideoInfo) {
  try {
    console.log('开始下载视频:', video.key)
    const result = await startVideoDownload(video, { silent: false })
    if (result.success) {
      ElMessage.success(`开始下载: ${video.displayName}`)
      // 下载开始后刷新本地视频列表
      setTimeout(() => {
        loadLocalVideos()
      }, 1000)
    }
  } catch (error) {
    console.error('Failed to download video:', error)
    ElMessage.error('下载视频失败')
  }
}

// 删除本地视频
async function deleteVideo(videoKey: string) {
  try {
    console.log('删除视频:', videoKey)
    const result = await deleteLocalVideo(videoKey, { silent: false })
    if (result.success) {
      await loadLocalVideos()
      emit('video-deleted', videoKey)
    }
  } catch (error) {
    console.error('Failed to delete video:', error)
    ElMessage.error('删除视频失败')
  }
}

// 选择视频
function selectVideo(video: LocalVideoInfo) {
  console.log('选择视频:', video.key)
  emit('video-selected', video)
}

// 检查视频是否已下载
function isVideoDownloaded(videoKey: string): boolean {
  return localVideos.value.some(v => v.key === videoKey)
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  return VideoService.formatFileSize(bytes)
}

// 格式化日期
function formatDate(dateString: string): string {
  return VideoService.formatDate(dateString)
}

// 初始化
onMounted(() => {
  console.log('VideoManager 组件已挂载')
  loadLocalVideos()
  loadRemoteVideos()
})

// 暴露方法给父组件
defineExpose({
  loadRemoteVideos,
  loadLocalVideos,
  remoteVideos,
  localVideos,
})
</script>

<template>
  <div class="video-manager">
    <!-- 本地视频列表 -->
    <LocalVideoList
      :videos="localVideos"
      :selected-video-key="selectedVideoKey"
      @video-selected="selectVideo"
      @video-deleted="deleteVideo"
    />

    <!-- 远程视频列表 -->
    <div class="remote-videos">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-medium">
          在线视频库 ({{ remoteVideos.length }})
        </h4>
        <ElButton
          :loading="isLoadingVideos"
          @click="loadRemoteVideos"
        >
          刷新列表
        </ElButton>
      </div>

      <div v-if="isLoadingVideos" class="py-8 text-center">
        <div class="loading-spinner mx-auto mb-4" />
        <div class="text-gray-500">
          加载视频列表中...
        </div>
      </div>

      <div v-else-if="remoteVideos.length === 0" class="py-8 text-center text-gray-500">
        暂无可用视频
      </div>

      <ElTable v-else :data="remoteVideos" stripe class="w-full">
        <ElTableColumn prop="displayName" label="视频名称" min-width="200" />
        <ElTableColumn prop="size" label="文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.size) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="lastModified" label="更新时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.lastModified) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="状态" width="100">
          <template #default="{ row }">
            <ElTag v-if="isVideoDownloaded(row.key)" type="success">
              已下载
            </ElTag>
            <ElTag v-else type="info">
              未下载
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="120">
          <template #default="{ row }">
            <ElButton
              v-if="!isVideoDownloaded(row.key)"
              type="primary"
              size="small"
              @click="downloadVideo(row)"
            >
              下载
            </ElButton>
            <ElButton
              v-else
              type="success"
              size="small"
              disabled
            >
              已下载
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>
  </div>
</template>

<style scoped>
.video-manager {
  space-y: 24px;
}

.remote-videos {
  margin-top: 24px;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--el-border-color-lighter);
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
